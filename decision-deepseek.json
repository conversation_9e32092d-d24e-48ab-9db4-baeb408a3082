{"name": "decision-deepseek", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.body.mensagem }}", "messages": {"messageValues": [{"message": "{\"role\": \"system\", \"content\": \"Você é um classificador de mensagens para uso comercial. Analise a mensagem do usuário e classifique-a em um dos tipos: saudação (greeting), consulta (inquiry), reclama<PERSON> (complaint), suporte (support), venda (sales), agradecimento (thanks), feedback (feedback), cancelamento (cancellation), spam (spam), RH (human resources), Logística (logistics), Assistência Técnica (technical assistance), Financeiro (financial), TI (information technology) ou outro (other). Priorize a categoria mais específica. Exemplos de consulta: perguntas sobre produtos, preços, informações ou verificações. Escolha o foco principal se múltiplas categorias. Extraia palavras relevantes (substantivos, verbos chave, palavras-chave), sem redundâncias, incluindo códigos de produtos e ações como 'tem'. Liste-as por ordem de relevância, separadas por vírgula. Extraia a intenção principal em frase curta. Identifique produto (código/nome) no campo 'produto' e quantidade (número exato) no campo 'quantidade'. Para saudação, gere resposta amigável no campo 'resposta'. Identifique o departamento: vendas (sales), financeiro (finance), logística (logistics), recursos humanos (human resources), tecnologia da informação (information technology), assistência técnica (technical assistance) ou outro (other). Inclua a data e hora atuais no formato 'DD/MM/YYYY HH:mm:ss' no campo 'data_hora'. Responda no formato JSON: { \\\"tipo\\\": \\\"<tipo em minúsculas>\\\", \\\"palavras\\\": \\\"<palavras separadas por vírgula>\\\", \\\"intencao\\\": \\\"<intenção curta>\\\", \\\"produto\\\": \\\"<produto, se aplicável>\\\", \\\"quantidade\\\": \\\"<quantidade, se aplicável>\\\", \\\"departamento\\\": \\\"<departamento em minúsculas>\\\", \\\"data_hora\\\": \\\"DD/MM/YYYY HH:mm:ss\\\", \\\"resposta\\\": \\\"<resposta, se saudação>\\\" }. Omita 'produto', 'quantidade' e 'resposta' se não aplicáveis.\"}"}, {"message": "={{ $json.mensagem }}"}, {"type": "HumanMessagePromptTemplate", "message": "={{ $json.body.mensagem }}"}]}, "batching": {}}, "id": "2c6f235c-4d02-4a58-a688-be3b2abeba4c", "name": "Classificar Mensagem", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [848, 208]}, {"parameters": {"options": {}}, "id": "e191263e-0bc1-4ea9-af55-3079b3afb09a", "name": "DeepSeek Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [848, 368], "credentials": {"deepSeekApi": {"id": "DkU8FmZKTZKWOLZa", "name": "DeepSeek account"}}}, {"parameters": {"httpMethod": "POST", "path": "94f88f74-fbfd-4b66-82d6-badd3bb465ca", "responseMode": "lastNode", "options": {}}, "id": "2a24f449-260e-42be-95aa-5ba62257d46b", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [608, 208], "webhookId": "94f88f74-fbfd-4b66-82d6-badd3bb465ca"}, {"parameters": {"respondWith": "json", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [1232, 208], "id": "9c18053f-4b53-4a32-a662-29077b9ce733", "name": "Respond to Webhook"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "const text = item.text; const match = text.match(/```json\\n([\\s\\S]*?)\\n```/); let jsonStr = match ? match[1] : text; jsonStr = jsonStr.trim(); const parsed = JSON.parse(jsonStr); parsed.data_hora = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo', day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(',', ''); return parsed;"}, "id": "b98e4f5a-7d3c-4e1b-9f2d-6e8f3a2b1c4d", "name": "Extract JSON", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1040, 208]}], "pinData": {}, "connections": {"DeepSeek Chat Model": {"ai_languageModel": [[{"node": "Classificar Mensagem", "type": "ai_languageModel", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Classificar Mensagem", "type": "main", "index": 0}]]}, "Classificar Mensagem": {"main": [[{"node": "Extract JSON", "type": "main", "index": 0}]]}, "Extract JSON": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "b2f1dadf-2e93-4c2e-b0bf-eb4c2b531359", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f37ca28da75d884f4821bdf9e9e882d2f2a44c4e233cf07517f857e13d8b41b1"}, "id": "WJ0X4GDg9gdYig8H", "tags": []}